<template>
    <div class="common-dialog-test">
        <h2>CommonDialog与DialogManager集成测试</h2>
        
        <div class="test-buttons">
            <button @click="showDialog1" class="test-btn">显示弹窗1</button>
            <button @click="showDialog2" class="test-btn">显示弹窗2</button>
            <button @click="showDialog3" class="test-btn">显示弹窗3（禁止关闭）</button>
            <button @click="closeLastDialog" class="test-btn">关闭最后一个弹窗</button>
            <button @click="closeAllDialogs" class="test-btn">关闭所有弹窗</button>
            <button @click="checkStatus" class="test-btn">检查状态</button>
        </div>

        <div class="status-info">
            <h3>状态信息</h3>
            <pre>{{ statusInfo }}</pre>
        </div>

        <!-- 测试弹窗1 -->
        <CommonDialog 
            v-model="dialog1Visible"
            title="测试弹窗1"
            message="这是第一个测试弹窗，用于验证DialogManager集成"
            :show-confirm-button="true"
            :show-reject-button="true"
            confirm-button-text="确定"
            reject-button-text="取消"
            @confirm="handleDialog1Confirm"
            @reject="handleDialog1Reject"
        />

        <!-- 测试弹窗2 -->
        <CommonDialog 
            v-model="dialog2Visible"
            title="测试弹窗2"
            message="这是第二个测试弹窗，测试多弹窗管理"
            :show-confirm-button="true"
            :show-cancel-button="true"
            confirm-button-text="好的"
            @confirm="handleDialog2Confirm"
            @cancel="handleDialog2Cancel"
        />

        <!-- 测试弹窗3 - 禁止关闭 -->
        <CommonDialog 
            v-model="dialog3Visible"
            title="测试弹窗3（禁止关闭）"
            message="这个弹窗设置了forbiddenClose=true，测试DialogManager的canClose功能"
            :show-confirm-button="true"
            :show-reject-button="true"
            :forbidden-close="true"
            confirm-button-text="强制关闭"
            reject-button-text="取消"
            @confirm="handleDialog3ForceClose"
            @reject="handleDialog3Reject"
        />
    </div>
</template>

<script>
import CommonDialog from './commonDialog.vue'

export default {
    name: 'CommonDialogTest',
    components: {
        CommonDialog
    },
    data() {
        return {
            dialog1Visible: false,
            dialog2Visible: false,
            dialog3Visible: false,
            statusInfo: ''
        }
    },
    methods: {
        showDialog1() {
            this.dialog1Visible = true;
            console.log('显示弹窗1');
        },
        showDialog2() {
            this.dialog2Visible = true;
            console.log('显示弹窗2');
        },
        showDialog3() {
            this.dialog3Visible = true;
            console.log('显示弹窗3（禁止关闭）');
        },
        closeLastDialog() {
            if (window.DialogManager) {
                const success = window.DialogManager.closeLastDialog();
                console.log('关闭最后一个弹窗:', success ? '成功' : '失败');
            } else {
                console.log('DialogManager不可用');
            }
        },
        closeAllDialogs() {
            if (window.DialogManager) {
                const count = window.DialogManager.closeAllDialogs();
                console.log('关闭了', count, '个弹窗');
            } else {
                console.log('DialogManager不可用');
            }
        },
        checkStatus() {
            let status = {
                dialog1Visible: this.dialog1Visible,
                dialog2Visible: this.dialog2Visible,
                dialog3Visible: this.dialog3Visible,
                currentDialogListLength: this.$root.currentDialogList ? this.$root.currentDialogList.length : 0
            };

            if (window.DialogManager) {
                status.dialogManagerStatus = window.DialogManager.getStatus();
                status.openDialogs = window.DialogManager.getOpenDialogs().length;
                status.hasOpenDialogs = window.DialogManager.hasOpenDialogs();
            } else {
                status.dialogManagerStatus = 'DialogManager不可用';
            }

            this.statusInfo = JSON.stringify(status, null, 2);
            console.log('当前状态:', status);
        },
        handleDialog1Confirm() {
            console.log('弹窗1：用户点击确定');
            this.dialog1Visible = false;
        },
        handleDialog1Reject() {
            console.log('弹窗1：用户点击取消');
            this.dialog1Visible = false;
        },
        handleDialog2Confirm() {
            console.log('弹窗2：用户点击好的');
            this.dialog2Visible = false;
        },
        handleDialog2Cancel() {
            console.log('弹窗2：用户点击取消按钮');
            this.dialog2Visible = false;
        },
        handleDialog3ForceClose() {
            console.log('弹窗3：用户点击强制关闭');
            // 通过ref获取组件实例并强制关闭
            this.dialog3Visible = false;
        },
        handleDialog3Reject() {
            console.log('弹窗3：用户点击取消（但由于forbiddenClose=true，可能无法关闭）');
            this.dialog3Visible = false;
        }
    },
    mounted() {
        console.log('CommonDialog测试组件已挂载');
        this.checkStatus();
    }
}
</script>

<style scoped>
.common-dialog-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.test-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.test-btn {
    padding: 10px 15px;
    background-color: #00c59d;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.test-btn:hover {
    background-color: #00a085;
}

.status-info {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
}

.status-info h3 {
    margin-top: 0;
    color: #333;
}

.status-info pre {
    background-color: white;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
}
</style>
