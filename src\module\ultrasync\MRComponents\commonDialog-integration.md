# CommonDialog 与 DialogManager 集成说明

## 概述

`commonDialog.vue` 组件现已完全集成到 DialogManager 中，实现了双模式兼容：
- **传统模式**：使用 `$root.currentDialogList` 管理弹窗
- **DialogManager模式**：使用 DialogManager 进行统一管理

## 集成特性

### 1. 自动注册与注销
- 当弹窗显示时（`v-model` 为 `true`），自动注册到 DialogManager
- 当弹窗隐藏时（`v-model` 为 `false`），自动从 DialogManager 注销
- 组件销毁时自动清理所有注册

### 2. 双模式兼容
```javascript
// 传统模式：添加到currentDialogList
this.$root.currentDialogList.push({
    id: this.currentDialogId,
    el: this
})

// DialogManager模式：注册到DialogManager
if (DialogManager) {
    this.dialogManagerId = DialogManager.register(this, {
        open: () => {
            // 弹窗已经通过v-model打开，这里不需要额外操作
        },
        close: () => {
            this.closeDialog();
        },
        canCloseOnPopstate: this.closeOnPopstate && !this.forbiddenClose,
        canClose: !this.forbiddenClose,
        id: this.currentDialogId
    });
}
```

### 3. 生命周期管理
- **创建时**：生成唯一ID，同时注册到传统模式和DialogManager
- **显示时**：触发注册逻辑
- **隐藏时**：触发注销逻辑
- **销毁时**：清理所有注册，防止内存泄漏

## 新增功能

### 1. DialogManager支持方法
```javascript
// 更新弹窗属性
updateDialogProperties(properties)

// 强制关闭弹窗（绕过canClose检查）
forceCloseDialog()

// 获取DialogManager注册ID
getDialogManagerId()
```

### 2. 属性映射
- `closeOnPopstate` + `forbiddenClose` → `canCloseOnPopstate`
- `forbiddenClose` → `canClose`

## 使用方式

### 基本用法（无变化）
```vue
<template>
    <CommonDialog 
        v-model="dialogVisible"
        title="标题"
        message="消息内容"
        :show-confirm-button="true"
        :show-reject-button="true"
        @confirm="handleConfirm"
        @reject="handleReject"
    />
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false
        }
    },
    methods: {
        handleConfirm() {
            this.dialogVisible = false;
        },
        handleReject() {
            this.dialogVisible = false;
        }
    }
}
</script>
```

### DialogManager控制
```javascript
// 通过DialogManager关闭最后一个弹窗
window.DialogManager.closeLastDialog();

// 关闭所有弹窗
window.DialogManager.closeAllDialogs();

// 检查弹窗状态
window.DialogManager.hasOpenDialogs();
```

## 测试验证

使用 `commonDialog-test.vue` 组件进行测试：

1. **多弹窗管理**：同时打开多个弹窗，验证管理功能
2. **禁止关闭测试**：测试 `forbiddenClose` 属性的效果
3. **状态检查**：实时查看弹窗状态和DialogManager状态
4. **批量关闭**：测试通过DialogManager批量关闭功能

## 兼容性

- ✅ **向后兼容**：现有使用方式无需修改
- ✅ **渐进增强**：DialogManager可用时自动启用，不可用时降级到传统模式
- ✅ **双重保障**：同时支持传统模式和DialogManager模式

## 注意事项

1. **不要手动调用注册/注销**：组件会自动处理生命周期
2. **使用v-model控制显示**：通过数据绑定控制弹窗显示/隐藏
3. **组件销毁前会自动清理**：无需担心内存泄漏
4. **DialogManager优先**：当DialogManager可用时，优先使用其功能

## 调试信息

组件会在控制台输出相关日志，便于调试：
- 注册成功时的日志
- 注销成功时的日志
- DialogManager不可用时的警告

通过这种集成方式，`commonDialog` 组件既保持了原有的简单易用性，又获得了 DialogManager 的强大管理能力。
