/**
 * Dialog修复测试 - 验证通过DialogManager关闭移动端弹窗是否正常工作
 */

import { openMobileDialog, closeMobileDialog } from '@/common/dialog'

// 测试函数：验证移动端弹窗的关闭功能
function testMobileDialogClose() {
    console.log('开始测试移动端弹窗关闭功能...');
    
    // 打开一个移动端弹窗
    const dialogId = openMobileDialog({
        title: '测试弹窗',
        message: '这是一个测试弹窗，用于验证关闭功能',
        showConfirmButton: true,
        showRejectButton: true,
        confirmButtonText: '确定',
        rejectButtonText: '取消',
        closeOnPopstate: true,
        confirm: () => {
            console.log('用户点击了确定按钮');
        },
        reject: () => {
            console.log('用户点击了取消按钮');
        },
        cancel: () => {
            console.log('用户取消了操作');
        }
    });
    
    console.log('弹窗已打开，ID:', dialogId);
    
    // 3秒后通过DialogManager关闭弹窗
    setTimeout(() => {
        console.log('3秒后通过DialogManager关闭弹窗...');
        const success = closeMobileDialog();
        console.log('关闭结果:', success ? '成功' : '失败');
        
        // 检查弹窗是否真的关闭了
        setTimeout(() => {
            if (window.DialogManager) {
                const hasDialogs = window.DialogManager.hasOpenDialogs();
                console.log('关闭后是否还有打开的弹窗:', hasDialogs);
                
                if (!hasDialogs) {
                    console.log('✅ 测试通过：弹窗已成功关闭');
                } else {
                    console.log('❌ 测试失败：弹窗仍然打开');
                }
            } else {
                console.log('⚠️ DialogManager不可用，无法验证状态');
            }
        }, 100);
    }, 3000);
}

// 测试函数：验证functionalDialog的closeDialog方法是否被正确调用
function testFunctionalDialogClose() {
    console.log('开始测试functionalDialog关闭方法...');
    
    if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
        // 备份原始的closeDialog方法
        const originalCloseDialog = window.vm.$root.functionalDialog.closeDialog;
        
        // 创建一个包装方法来监控调用
        window.vm.$root.functionalDialog.closeDialog = function() {
            console.log('✅ functionalDialog.closeDialog() 被调用了');
            return originalCloseDialog.apply(this, arguments);
        };
        
        // 打开弹窗
        const dialogId = openMobileDialog({
            title: '测试functionalDialog关闭',
            message: '测试functionalDialog的closeDialog方法是否被正确调用',
            showConfirmButton: true,
            confirmButtonText: '确定',
            confirm: () => {
                console.log('用户点击了确定');
            }
        });
        
        // 2秒后通过DialogManager关闭
        setTimeout(() => {
            console.log('通过DialogManager关闭弹窗...');
            closeMobileDialog();
            
            // 恢复原始方法
            setTimeout(() => {
                window.vm.$root.functionalDialog.closeDialog = originalCloseDialog;
                console.log('已恢复原始的closeDialog方法');
            }, 500);
        }, 2000);
        
    } else {
        console.log('⚠️ functionalDialog不可用，跳过测试');
    }
}

// 测试函数：验证DialogManager的状态管理
function testDialogManagerStatus() {
    console.log('开始测试DialogManager状态管理...');
    
    if (window.DialogManager) {
        console.log('初始状态:', window.DialogManager.getStatus());
        
        // 打开弹窗
        const dialogId = openMobileDialog({
            title: '状态测试',
            message: '测试DialogManager的状态管理',
            showConfirmButton: true,
            confirmButtonText: '确定'
        });
        
        setTimeout(() => {
            console.log('打开弹窗后的状态:', window.DialogManager.getStatus());
            console.log('打开的弹窗列表:', window.DialogManager.getOpenDialogs());
            
            // 关闭弹窗
            closeMobileDialog();
            
            setTimeout(() => {
                console.log('关闭弹窗后的状态:', window.DialogManager.getStatus());
                console.log('打开的弹窗列表:', window.DialogManager.getOpenDialogs());
            }, 100);
        }, 1000);
        
    } else {
        console.log('⚠️ DialogManager不可用，跳过测试');
    }
}

// 导出测试函数
export {
    testMobileDialogClose,
    testFunctionalDialogClose,
    testDialogManagerStatus
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.dialogTests = {
        testMobileDialogClose,
        testFunctionalDialogClose,
        testDialogManagerStatus
    };
    
    console.log('Dialog测试函数已添加到 window.dialogTests');
    console.log('可用的测试函数:');
    console.log('- window.dialogTests.testMobileDialogClose()');
    console.log('- window.dialogTests.testFunctionalDialogClose()');
    console.log('- window.dialogTests.testDialogManagerStatus()');
}
