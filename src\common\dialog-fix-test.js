/**
 * Dialog修复测试 - 验证弹窗关闭行为在注册时正确绑定
 *
 * 修复说明：
 * 1. 弹窗的关闭行为现在在注册时就确定，而不是在DialogManager中写死
 * 2. 每个弹窗实例在打开时会保存自己的引用，关闭时使用自己的closeDialog方法
 * 3. 这样确保了每个弹窗都有自己独立的关闭逻辑
 */

import { openMobileDialog, closeMobileDialog } from '@/common/dialog'

// 测试函数：验证移动端弹窗的关闭功能
function testMobileDialogClose() {
    console.log('开始测试移动端弹窗关闭功能...');

    // 打开一个移动端弹窗
    const dialogId = openMobileDialog({
        title: '测试弹窗',
        message: '这是一个测试弹窗，用于验证关闭功能',
        showConfirmButton: true,
        showRejectButton: true,
        confirmButtonText: '确定',
        rejectButtonText: '取消',
        closeOnPopstate: true,
        confirm: () => {
            console.log('用户点击了确定按钮');
        },
        reject: () => {
            console.log('用户点击了取消按钮');
        },
        cancel: () => {
            console.log('用户取消了操作');
        }
    });

    console.log('弹窗已打开，ID:', dialogId);

    // 3秒后通过DialogManager关闭弹窗
    setTimeout(() => {
        console.log('3秒后通过DialogManager关闭弹窗...');
        const success = closeMobileDialog();
        console.log('关闭结果:', success ? '成功' : '失败');

        // 检查弹窗是否真的关闭了
        setTimeout(() => {
            if (window.DialogManager) {
                const hasDialogs = window.DialogManager.hasOpenDialogs();
                console.log('关闭后是否还有打开的弹窗:', hasDialogs);

                if (!hasDialogs) {
                    console.log('✅ 测试通过：弹窗已成功关闭');
                } else {
                    console.log('❌ 测试失败：弹窗仍然打开');
                }
            } else {
                console.log('⚠️ DialogManager不可用，无法验证状态');
            }
        }, 100);
    }, 3000);
}

// 测试函数：验证functionalDialog的closeDialog方法是否被正确调用
function testFunctionalDialogClose() {
    console.log('开始测试functionalDialog关闭方法...');

    if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
        // 备份原始的closeDialog方法
        const originalCloseDialog = window.vm.$root.functionalDialog.closeDialog;

        // 创建一个包装方法来监控调用
        window.vm.$root.functionalDialog.closeDialog = function() {
            console.log('✅ functionalDialog.closeDialog() 被调用了');
            return originalCloseDialog.apply(this, arguments);
        };

        // 打开弹窗
        const dialogId = openMobileDialog({
            title: '测试functionalDialog关闭',
            message: '测试functionalDialog的closeDialog方法是否被正确调用',
            showConfirmButton: true,
            confirmButtonText: '确定',
            confirm: () => {
                console.log('用户点击了确定');
            }
        });

        // 2秒后通过DialogManager关闭
        setTimeout(() => {
            console.log('通过DialogManager关闭弹窗...');
            closeMobileDialog();

            // 恢复原始方法
            setTimeout(() => {
                window.vm.$root.functionalDialog.closeDialog = originalCloseDialog;
                console.log('已恢复原始的closeDialog方法');
            }, 500);
        }, 2000);

    } else {
        console.log('⚠️ functionalDialog不可用，跳过测试');
    }
}

// 测试函数：验证DialogManager的状态管理
function testDialogManagerStatus() {
    console.log('开始测试DialogManager状态管理...');

    if (window.DialogManager) {
        console.log('初始状态:', window.DialogManager.getStatus());

        // 打开弹窗
        const dialogId = openMobileDialog({
            title: '状态测试',
            message: '测试DialogManager的状态管理',
            showConfirmButton: true,
            confirmButtonText: '确定'
        });

        setTimeout(() => {
            console.log('打开弹窗后的状态:', window.DialogManager.getStatus());
            console.log('打开的弹窗列表:', window.DialogManager.getOpenDialogs());

            // 关闭弹窗
            closeMobileDialog();

            setTimeout(() => {
                console.log('关闭弹窗后的状态:', window.DialogManager.getStatus());
                console.log('打开的弹窗列表:', window.DialogManager.getOpenDialogs());
            }, 100);
        }, 1000);

    } else {
        console.log('⚠️ DialogManager不可用，跳过测试');
    }
}

// 测试函数：验证弹窗实例绑定
function testDialogInstanceBinding() {
    console.log('开始测试弹窗实例绑定...');

    if (window.DialogManager && window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
        // 创建一个自定义的closeDialog方法来验证绑定
        const originalCloseDialog = window.vm.$root.functionalDialog.closeDialog;
        let closeCallCount = 0;

        window.vm.$root.functionalDialog.closeDialog = function() {
            closeCallCount++;
            console.log(`✅ 弹窗实例的closeDialog被调用了 (第${closeCallCount}次)`);
            return originalCloseDialog.apply(this, arguments);
        };

        // 打开第一个弹窗
        const dialogId1 = openMobileDialog({
            title: '实例绑定测试 1',
            message: '这是第一个弹窗，测试实例绑定',
            showConfirmButton: true,
            confirmButtonText: '确定'
        });

        setTimeout(() => {
            // 打开第二个弹窗
            const dialogId2 = openMobileDialog({
                title: '实例绑定测试 2',
                message: '这是第二个弹窗，测试实例绑定',
                showConfirmButton: true,
                confirmButtonText: '确定'
            });

            setTimeout(() => {
                console.log('当前打开的弹窗数量:', window.DialogManager.getOpenDialogs().length);

                // 关闭最后一个弹窗
                console.log('关闭最后一个弹窗...');
                closeMobileDialog();

                setTimeout(() => {
                    console.log('剩余打开的弹窗数量:', window.DialogManager.getOpenDialogs().length);

                    // 关闭剩余的弹窗
                    console.log('关闭剩余弹窗...');
                    closeMobileDialog();

                    setTimeout(() => {
                        console.log('最终打开的弹窗数量:', window.DialogManager.getOpenDialogs().length);
                        console.log(`总共调用了 ${closeCallCount} 次closeDialog方法`);

                        // 恢复原始方法
                        window.vm.$root.functionalDialog.closeDialog = originalCloseDialog;
                        console.log('已恢复原始的closeDialog方法');
                    }, 100);
                }, 100);
            }, 1000);
        }, 1000);

    } else {
        console.log('⚠️ DialogManager或functionalDialog不可用，跳过测试');
    }
}

// 导出测试函数
export {
    testMobileDialogClose,
    testFunctionalDialogClose,
    testDialogManagerStatus,
    testDialogInstanceBinding
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.dialogTests = {
        testMobileDialogClose,
        testFunctionalDialogClose,
        testDialogManagerStatus,
        testDialogInstanceBinding
    };

    console.log('Dialog测试函数已添加到 window.dialogTests');
    console.log('可用的测试函数:');
    console.log('- window.dialogTests.testMobileDialogClose()');
    console.log('- window.dialogTests.testFunctionalDialogClose()');
    console.log('- window.dialogTests.testDialogManagerStatus()');
    console.log('- window.dialogTests.testDialogInstanceBinding()');
}
