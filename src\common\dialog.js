// dialog.js - 重构版本，集成DialogManager
let DialogId = null;
import { Dialog } from 'vant';
import Tool from '@/common/tool'

// 尝试导入DialogManager，如果不存在则使用传统模式
let DialogManager = null;
try {
    DialogManager = require('@/module/ultrasync/lib/dialogManager').default;
} catch (e) {
    console.warn('DialogManager not available, using legacy mode');
}
/**
 * 平台适配器 - 统一不同平台的弹窗处理
 */
class PlatformAdapter {
    static detectPlatform() {
        const isCef = window.vm.$store.state.globalParams.isCef;
        if (isCef) {
            return 'cef';
        }
        if (Tool.checkAppClient('PCBrowser')) {
            return 'pcBrowser';
        }
        if (Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')) {
            return 'mobile';
        }
        return 'unknown';
    }

    static showDialog(platform, config) {
        switch (platform) {
        case 'cef':
            return this.showCefDialog(config);
        case 'pcBrowser':
            return this.showPCBrowserDialog(config);
        case 'mobile':
            return this.showMobileDialog(config);
        default:
            return this.showFallbackDialog(config);
        }
    }

    static showCefDialog(config) {
        const params = {
            title: config.title,
            message: config.message,
            des: config.des || 'common'
        };

        if (config.buttons[0]) {
            params.yes_button = config.buttons[0];
        }
        if (config.buttons[1]) {
            params.no_button = config.buttons[1];
        }
        if (config.buttons[2]) {
            params.append_button = config.buttons[2];
        }

        Tool.createCWorkstationCommunicationMng({
            name: 'ShowConfirmDialog',
            emitName: 'NotifyShowConfirmDialogByUser',
            params,
            timeout: null,
            des: 'common'
        }).then((res) => {
            if (res.no === 0) {
                config.confirm();
            } else if (res.no === 1) {
                config.reject();
            } else {
                config.cancel();
            }
        });
    }

    static showPCBrowserDialog(config) {
        window.vm.$MessageBox.confirm(config.message, config.title, {
            confirmButtonText: config.buttons[0],
            cancelButtonText: config.buttons[1],
            showCancelButton: !!config.buttons[1],
            showConfirmButton: !!config.buttons[0],
            closeOnClickModal: false,
            customClass: config.id,
            callback: (action) => {
                if (action === 'confirm') {
                    config.confirm();
                } else if (action === 'cancel') {
                    config.reject();
                }
            }
        });
    }

    static showMobileDialog(config) {
        if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
            window.vm.$root.functionalDialog.showDialog({
                title: config.title,
                message: config.message,
                closeOnPopstate: true,
                showConfirmButton: !!config.buttons[0],
                showRejectButton: !!config.buttons[1],
                confirmButtonText: config.buttons[0],
                rejectButtonText: config.buttons[1],
                confirm: config.confirm,
                reject: config.reject,
                cancel: config.cancel
            });
        } else {
            Dialog.confirm({
                title: config.title,
                message: config.message,
                closeOnPopstate: true,
                showConfirmButton: !!config.buttons[0],
                showCancelButton: !!config.buttons[1],
                confirmButtonText: config.buttons[0],
                cancelButtonText: config.buttons[1]
            }).then(() => {
                config.confirm();
            }).catch(() => {
                config.reject();
            });
        }
    }

    static showFallbackDialog(config) {
        // 使用浏览器原生confirm作为后备方案
        const result = confirm(`${config.title}\n${config.message}`);
        if (result) {
            config.confirm();
        } else {
            config.reject();
        }
    }
}

export function openCommonDialog({
    buttons = [],
    title = '',
    message = '',
    des = 'common',
    confirm = function(){},
    reject = function(){},
    cancel = function(){},
    id = ''
}) {
    const lang = window.vm.$store.state.language;

    if (!Array.isArray(buttons)) {
        console.warn('openCommonDialog: buttons must be an array');
        return null;
    }

    DialogId = id || Tool.genID(3);

    // 包装原始回调函数，在执行时自动注销dialog
    const wrappedConfirm = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        confirm();
    };

    const wrappedReject = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        reject();
    };

    const wrappedCancel = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        cancel();
    };

    const config = {
        id: DialogId,
        title: title || lang.tip_title,
        message,
        des,
        buttons,
        confirm: wrappedConfirm,
        reject: wrappedReject,
        cancel: wrappedCancel
    };

    const platform = PlatformAdapter.detectPlatform();

    // 如果有DialogManager，注册dialog
    if (DialogManager) {
        DialogManager.register(config, {
            open: () => PlatformAdapter.showDialog(platform, config),
            close: () => {
                // 根据平台执行相应的关闭操作
                if (platform === 'cef') {
                    try {
                        window.CWorkstationCommunicationMng.RemoveConfirmDialog();
                    } catch (error) {
                        console.error('Error closing CEF dialog:', error);
                    }
                } else if (platform === 'pcBrowser') {
                    // PC浏览器的MessageBox会自动处理关闭
                } else if (platform === 'mobile') {
                    if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
                        // 移动端functionalDialog的关闭由其内部处理
                    } else {
                        Dialog.close();
                    }
                }
            },
            canCloseOnPopstate: true,
            canClose: true,
            id: DialogId
        });
    } else {
        // 后备方案：直接显示dialog
        PlatformAdapter.showDialog(platform, config);
    }

    return DialogId;
}

export function getCommonDialogId(){
    return DialogId
}

export function openMobileDialog({
    showRejectButton = false,
    showConfirmButton = true,
    showCancelButton = true,
    confirmButtonText = '',
    rejectButtonText = '',
    title = '',
    beforeClose = null,
    confirm = null,
    reject = null,
    cancel = null,
    message = '',
    close = null,
    messageAlign = 'center',
    closeOnClickOverlay = false,
    closeOnPopstate = true,
} = {}) {
    // 只在移动端环境下工作
    if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
        console.warn('openMobileDialog: Not in mobile environment');
        return null;
    }

    DialogId = Tool.genID(3);
    const lang = window.vm.$store.state.language;

    // 包装原始回调函数，在执行时自动注销dialog
    const wrappedConfirm = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        confirm && confirm();
    };

    const wrappedReject = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        reject && reject();
    };

    const wrappedCancel = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        cancel && cancel();
    };

    const wrappedClose = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        close && close();
    };

    const config = {
        showRejectButton,
        showConfirmButton,
        showCancelButton,
        confirmButtonText,
        rejectButtonText,
        title: title || lang.tip_title,
        beforeClose,
        message,
        close: wrappedClose,
        messageAlign,
        closeOnClickOverlay,
        confirm: wrappedConfirm,
        reject: wrappedReject,
        cancel: wrappedCancel,
        closeOnPopstate
    };

    // 如果有DialogManager，注册dialog
    if (DialogManager) {
        DialogManager.register(config, {
            open: () => {
                if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
                    window.vm.$root.functionalDialog.showDialog(config);
                } else {
                    // 后备方案：使用vant Dialog
                    Dialog.confirm({
                        title: config.title,
                        message: config.message,
                        closeOnPopstate: config.closeOnPopstate,
                        showConfirmButton: config.showConfirmButton,
                        showCancelButton: config.showRejectButton,
                        confirmButtonText: config.confirmButtonText,
                        cancelButtonText: config.rejectButtonText,
                    }).then(() => {
                        config.confirm && config.confirm();
                    }).catch(() => {
                        config.reject && config.reject();
                        config.cancel && config.cancel();
                    });
                }
            },
            close: () => {
                if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
                    // functionalDialog的关闭由其内部处理
                } else {
                    Dialog.close();
                }
            },
            canCloseOnPopstate: closeOnPopstate,
            canClose: true,
            id: DialogId
        });
    } else {
        // 后备方案：直接显示dialog
        if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
            window.vm.$root.functionalDialog.showDialog(config);
        } else {
            // 后备方案：使用vant Dialog
            Dialog.confirm({
                title: config.title,
                message: config.message,
                closeOnPopstate: config.closeOnPopstate,
                showConfirmButton: config.showConfirmButton,
                showCancelButton: config.showRejectButton,
                confirmButtonText: config.confirmButtonText,
                cancelButtonText: config.rejectButtonText,
            }).then(() => {
                config.confirm && config.confirm();
            }).catch(() => {
                config.reject && config.reject();
                config.cancel && config.cancel();
            });
        }
    }

    return DialogId;
}
/**
 * 统一的弹窗关闭处理
 */
class DialogCloser {
    static closeAll() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.closeAllDialogs();
        }

        // 后备方案：传统方式
        return this.closeLegacyDialogs();
    }

    static closeLast() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.closeLastDialog();
        }

        // 后备方案：传统方式
        return this.closeLegacyLastDialog();
    }

    static closeLegacyDialogs() {
        let closedCount = 0;

        if (Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')) {
            if (window.vm && window.vm.$root && window.vm.$root.currentDialogList.length > 0) {
                window.vm.$root.currentDialogList.forEach(item => {
                    try {
                        item.el.closeDialog();
                        closedCount++;
                    } catch (error) {
                        console.error('Error closing dialog:', error);
                    }
                });
                window.vm.$root.currentDialogList = [];
            }
        }

        return closedCount;
    }

    static closeLegacyLastDialog() {
        if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
            return false;
        }

        try {
            if (window.vm && window.vm.$root && window.vm.$root.currentDialogList.length > 0) {
                const initialLength = window.vm.$root.currentDialogList.length;
                const lastDialog = window.vm.$root.currentDialogList[initialLength - 1];

                lastDialog.el.closeDialog();

                // 延迟检查是否需要手动清理
                setTimeout(() => {
                    if (window.vm.$root.currentDialogList.length === initialLength) {
                        window.vm.$root.currentDialogList.pop();
                    }
                }, 0);

                return true;
            } else {
                // 使用全局Dialog关闭
                Dialog.close();
                return true;
            }
        } catch (error) {
            console.error('Error closing the mobile dialog:', error);
            return false;
        }
    }

    static closeByPlatform() {
        const isCef = window.vm.$store.state.globalParams.isCef;

        if (isCef) {
            try {
                window.CWorkstationCommunicationMng.RemoveConfirmDialog();
                return true;
            } catch (error) {
                console.error('Error closing CEF dialog:', error);
                return false;
            }
        } else {
            return this.closeLast();
        }
    }
}

export function closeAllDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeAllDialogs();
    }
    // 后备方案
    return DialogCloser.closeAll();
}

export function closeMobileDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeLastDialog();
    }
    // 后备方案
    return DialogCloser.closeLast();
}

export function closeCommonDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeLastDialog();
    }
    // 后备方案
    return DialogCloser.closeByPlatform();
}

/**
 * 弹窗状态检查器
 */
class DialogChecker {
    static hasOpenDialogs() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.hasOpenDialogs();
        }

        // 后备方案：传统检查
        return this.checkLegacyDialogs();
    }

    static checkLegacyDialogs() {
        try {
            if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
                return false;
            }

            return !!(window.vm &&
                     window.vm.$root &&
                     window.vm.$root.currentDialogList &&
                     window.vm.$root.currentDialogList.length > 0);
        } catch (error) {
            console.error('Error checking legacy dialogs:', error);
            return false;
        }
    }

    static canCloseLastDialog() {
        // 优先使用DialogManager
        if (DialogManager) {
            const lastDialog = DialogManager.getLastOpenDialog();
            return lastDialog ? lastDialog[1].canClose : true;
        }

        // 后备方案：传统检查
        return this.checkLegacyCanClose();
    }

    static checkLegacyCanClose() {
        try {
            if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
                return true;
            }

            if (window.vm && window.vm.$root && window.vm.$root.currentDialogList.length > 0) {
                const lastDialog = window.vm.$root.currentDialogList[window.vm.$root.currentDialogList.length - 1];
                return lastDialog.el.checkCanClose() !== false;
            }

            return true;
        } catch (error) {
            console.error('Error checking can close:', error);
            return true;
        }
    }

    static canCloseOnPopstate() {
        // 优先使用DialogManager
        if (DialogManager) {
            const lastDialog = DialogManager.getLastOpenDialog();
            return lastDialog ? lastDialog[1].canCloseOnPopstate : true;
        }

        // 后备方案：传统检查
        return this.checkLegacyCanCloseOnPopstate();
    }

    static checkLegacyCanCloseOnPopstate() {
        try {
            if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
                return true;
            }

            if (window.vm && window.vm.$root && window.vm.$root.currentDialogList.length > 0) {
                const lastDialog = window.vm.$root.currentDialogList[window.vm.$root.currentDialogList.length - 1];
                return lastDialog.el.checkCanCloseOnPopstate() !== false;
            }

            return true;
        } catch (error) {
            console.error('Error checking can close on popstate:', error);
            return true;
        }
    }
}

export function checkMobileDialogShow() {
    return DialogChecker.hasOpenDialogs();
}

export function checkMobileCanCloseDialog() {
    return DialogChecker.canCloseLastDialog();
}

export function checkMobileCanCloseOnPopstate() {
    return DialogChecker.canCloseOnPopstate();
}

